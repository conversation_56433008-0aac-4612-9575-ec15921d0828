import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Chip,
  Box,
  Divider,
  Badge
} from '@material-ui/core';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Delete as DeleteIcon,
  ClearAll as ClearAllIcon,
  AssignmentTurnedIn,
  AssignmentTurnedInOutlined
} from '@material-ui/icons';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment-timezone';

const useStyles = makeStyles((theme) => ({
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationItem: {
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
    marginBottom: theme.spacing(1),
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  notificationContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(0.5),
  },
  notificationHeader: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(1),
  },
  actionChip: {
    fontSize: '0.75rem',
    height: '20px',
  },
  fillChip: {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
  },
  emptyChip: {
    backgroundColor: theme.palette.secondary.light,
    color: theme.palette.secondary.contrastText,
  },
  timestamp: {
    fontSize: '0.75rem',
    color: theme.palette.text.secondary,
  },
  emptyState: {
    textAlign: 'center',
    padding: theme.spacing(4),
    color: theme.palette.text.secondary,
  },
  clearAllButton: {
    marginRight: theme.spacing(1),
  },
}));

/**
 * Componente para mostrar el historial de notificaciones de tanques de agua
 */
const WaterTankNotificationHistory = ({ 
  open, 
  onClose, 
  notifications, 
  onRemoveNotification, 
  onClearAll,
  userTimezone = 'America/Mexico_City'
}) => {
  const classes = useStyles();

  // Obtener el icono apropiado según el tipo de notificación
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon style={{ color: '#4caf50' }} />;
      case 'error':
        return <ErrorIcon style={{ color: '#f44336' }} />;
      case 'warning':
        return <WarningIcon style={{ color: '#ff9800' }} />;
      case 'info':
      default:
        return <InfoIcon style={{ color: '#2196f3' }} />;
    }
  };

  // Formatear la fecha y hora
  const formatTimestamp = (timestamp) => {
    return moment(timestamp).tz(userTimezone).format('DD/MM/YYYY HH:mm:ss');
  };

  // Obtener el texto de la acción
  const getActionText = (action) => {
    switch (action) {
      case 'fill':
        return 'Llenado';
      case 'empty':
        return 'Vaciado';
      case 'recirculate':
        return 'Recirculación';
      default:
        return 'Operación';
    }
  };

  // Obtener la clase del chip según la acción
  const getChipClass = (action) => {
    switch (action) {
      case 'fill':
        return classes.fillChip;
      case 'empty':
        return classes.emptyChip;
      default:
        return classes.fillChip;
    }
  };

  // Ordenar notificaciones por timestamp (más recientes primero)
  const sortedNotifications = [...notifications].sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        style: { minHeight: '500px' }
      }}
    >
      <DialogTitle className={classes.dialogTitle}>
        <Box display="flex" alignItems="center" gap={1}>
          <AssignmentTurnedIn />
          <Typography variant="h6">
            Historial de Notificaciones de Tanques
          </Typography>
          <Badge badgeContent={notifications.length} color="primary" />
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {sortedNotifications.length === 0 ? (
          <div className={classes.emptyState}>
            <AssignmentTurnedInOutlined style={{ fontSize: 48, opacity: 0.3, marginBottom: 16 }} />
            <Typography variant="h6" gutterBottom>
              No hay notificaciones
            </Typography>
            <Typography variant="body2">
              Las notificaciones de operaciones de tanques aparecerán aquí
            </Typography>
          </div>
        ) : (
          <List>
            {sortedNotifications.map((notification, index) => (
              <ListItem key={notification.id} className={classes.notificationItem}>
                <ListItemIcon>
                  {getNotificationIcon(notification.type)}
                </ListItemIcon>
                
                <ListItemText>
                  <div className={classes.notificationContent}>
                    <div className={classes.notificationHeader}>
                      <Typography variant="subtitle2" component="span">
                        {notification.title}
                      </Typography>
                      {notification.action && (
                        <Chip
                          label={getActionText(notification.action)}
                          size="small"
                          className={`${classes.actionChip} ${getChipClass(notification.action)}`}
                        />
                      )}
                    </div>
                    
                    <Typography variant="body2" color="textSecondary">
                      {notification.message}
                    </Typography>
                    
                    {notification.tankName && (
                      <Typography variant="caption" color="textSecondary">
                        <strong>Tanque:</strong> {notification.tankName}
                      </Typography>
                    )}
                    
                    <Typography variant="caption" className={classes.timestamp}>
                      {formatTimestamp(notification.timestamp)}
                    </Typography>
                  </div>
                </ListItemText>

                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => onRemoveNotification(notification.id)}
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>

      <DialogActions>
        {notifications.length > 0 && (
          <Button
            onClick={onClearAll}
            color="secondary"
            startIcon={<ClearAllIcon />}
            className={classes.clearAllButton}
          >
            Limpiar Todo
          </Button>
        )}
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default WaterTankNotificationHistory;
